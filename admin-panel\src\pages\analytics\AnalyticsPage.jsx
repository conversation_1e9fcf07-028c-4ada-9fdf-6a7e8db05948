import { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import {
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  StarIcon,
  CalendarDaysIcon,
  TrendingUpIcon,
  EyeIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline'
import PremiumCard, { PremiumCardHeader, PremiumCardTitle, PremiumCardContent } from '../../components/ui/PremiumCard'
import PremiumButton from '../../components/ui/PremiumButton'

const AnalyticsPage = () => {
  const [analytics, setAnalytics] = useState(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  // Mock analytics data
  useEffect(() => {
    const mockAnalytics = {
      overview: {
        totalRevenue: 245000,
        revenueGrowth: 12.5,
        totalTasks: 1234,
        tasksGrowth: 8.2,
        activeUsers: 2847,
        usersGrowth: 15.3,
        avgRating: 4.7,
        ratingGrowth: 2.1
      },
      revenueChart: [
        { month: 'Jan', revenue: 35000, tasks: 180 },
        { month: 'Feb', revenue: 42000, tasks: 220 },
        { month: 'Mar', revenue: 38000, tasks: 195 },
        { month: 'Apr', revenue: 51000, tasks: 280 },
        { month: 'May', revenue: 49000, tasks: 265 },
        { month: 'Jun', revenue: 67000, tasks: 359 }
      ],
      topCategories: [
        { name: 'Plumbing', tasks: 456, revenue: 89000, growth: 15.2 },
        { name: 'Electrical', tasks: 342, revenue: 67000, growth: 8.7 },
        { name: 'Cleaning', tasks: 289, revenue: 45000, growth: 12.1 },
        { name: 'Carpentry', tasks: 198, revenue: 38000, growth: -2.3 },
        { name: 'Painting', tasks: 156, revenue: 28000, growth: 5.8 }
      ],
      topWorkBoys: [
        { name: 'Rajesh Kumar', tasks: 89, revenue: 18500, rating: 4.9 },
        { name: 'Amit Sharma', tasks: 76, revenue: 16200, rating: 4.8 },
        { name: 'Suresh Patel', tasks: 68, revenue: 14800, rating: 4.7 },
        { name: 'Vikram Singh', tasks: 62, revenue: 13900, rating: 4.8 },
        { name: 'Manoj Gupta', tasks: 45, revenue: 9800, rating: 4.5 }
      ],
      customerSatisfaction: {
        excellent: 68,
        good: 24,
        average: 6,
        poor: 2
      }
    }
    
    setTimeout(() => {
      setAnalytics(mockAnalytics)
      setLoading(false)
    }, 1000)
  }, [timeRange])

  const getGrowthIndicator = (growth) => {
    const isPositive = growth > 0
    const Icon = isPositive ? ArrowUpIcon : ArrowDownIcon
    const colorClass = isPositive ? 'text-success-600' : 'text-danger-600'
    
    return (
      <div className={`flex items-center ${colorClass}`}>
        <Icon className="h-3 w-3 mr-1" />
        <span className="text-xs font-bold">{Math.abs(growth)}%</span>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6 animate-fade-in-up">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-2xl p-6 border border-gray-200">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 animate-fade-in-up">
      <Helmet>
        <title>Analytics Dashboard - WorkBoy Admin</title>
      </Helmet>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
            Analytics Dashboard
          </h1>
          <p className="text-gray-600 mt-2 font-medium">Comprehensive business insights and performance metrics</p>
        </div>
        <div className="flex space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium bg-white focus:outline-none focus:ring-2 focus:ring-primary-500/20 focus:border-primary-300"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <PremiumButton variant="outline" icon={ArrowDownTrayIcon}>
            Export Report
          </PremiumButton>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <PremiumCard hover gradient>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Revenue</p>
              <p className="text-2xl font-black text-gray-900">₹{analytics.overview.totalRevenue.toLocaleString()}</p>
              {getGrowthIndicator(analytics.overview.revenueGrowth)}
            </div>
            <div className="p-3 rounded-xl bg-gradient-to-br from-success-500 to-success-600 shadow-lg shadow-success-500/25">
              <CurrencyDollarIcon className="h-6 w-6 text-white" />
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Total Tasks</p>
              <p className="text-2xl font-black text-gray-900">{analytics.overview.totalTasks.toLocaleString()}</p>
              {getGrowthIndicator(analytics.overview.tasksGrowth)}
            </div>
            <div className="p-3 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
              <ClipboardDocumentListIcon className="h-6 w-6 text-white" />
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Active Users</p>
              <p className="text-2xl font-black text-gray-900">{analytics.overview.activeUsers.toLocaleString()}</p>
              {getGrowthIndicator(analytics.overview.usersGrowth)}
            </div>
            <div className="p-3 rounded-xl bg-gradient-to-br from-warning-500 to-warning-600 shadow-lg shadow-warning-500/25">
              <UsersIcon className="h-6 w-6 text-white" />
            </div>
          </div>
        </PremiumCard>

        <PremiumCard hover gradient>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">Avg Rating</p>
              <p className="text-2xl font-black text-gray-900">{analytics.overview.avgRating}</p>
              {getGrowthIndicator(analytics.overview.ratingGrowth)}
            </div>
            <div className="p-3 rounded-xl bg-gradient-to-br from-accent-500 to-accent-600 shadow-lg shadow-accent-500/25">
              <StarIcon className="h-6 w-6 text-white" />
            </div>
          </div>
        </PremiumCard>
      </div>

      {/* Revenue Chart */}
      <PremiumCard gradient>
        <PremiumCardHeader>
          <PremiumCardTitle icon={ChartBarIcon}>
            Revenue & Tasks Trend
          </PremiumCardTitle>
        </PremiumCardHeader>
        <PremiumCardContent>
          <div className="space-y-4">
            {analytics.revenueChart.map((item, index) => (
              <div key={index} className="group">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-bold text-gray-700">{item.month}</div>
                  <div className="flex items-center space-x-4">
                    <div className="text-sm font-black text-gray-900">₹{item.revenue.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">{item.tasks} tasks</div>
                  </div>
                </div>
                <div className="relative">
                  <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div 
                      className="bg-gradient-to-r from-primary-500 via-primary-600 to-brand-600 h-3 rounded-full transition-all duration-1000 ease-out shadow-sm group-hover:shadow-primary-500/30"
                      style={{ 
                        width: `${(item.revenue / Math.max(...analytics.revenueChart.map(d => d.revenue))) * 100}%`,
                        animationDelay: `${index * 100}ms`
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </PremiumCardContent>
      </PremiumCard>

      {/* Top Categories and Work-Boys */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PremiumCard gradient>
          <PremiumCardHeader>
            <PremiumCardTitle icon={TrendingUpIcon}>
              Top Categories
            </PremiumCardTitle>
          </PremiumCardHeader>
          <PremiumCardContent>
            <div className="space-y-4">
              {analytics.topCategories.map((category, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20 rounded-xl hover:from-primary-50/30 hover:to-brand-50/20 transition-all duration-300">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">
                      {category.name === 'Plumbing' ? '🔧' : 
                       category.name === 'Electrical' ? '⚡' :
                       category.name === 'Cleaning' ? '🧹' :
                       category.name === 'Carpentry' ? '🔨' : '🎨'}
                    </div>
                    <div>
                      <div className="font-bold text-gray-900">{category.name}</div>
                      <div className="text-sm text-gray-500">{category.tasks} tasks</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-gray-900">₹{category.revenue.toLocaleString()}</div>
                    {getGrowthIndicator(category.growth)}
                  </div>
                </div>
              ))}
            </div>
          </PremiumCardContent>
        </PremiumCard>

        <PremiumCard gradient>
          <PremiumCardHeader>
            <PremiumCardTitle icon={StarIcon}>
              Top Work-Boys
            </PremiumCardTitle>
          </PremiumCardHeader>
          <PremiumCardContent>
            <div className="space-y-4">
              {analytics.topWorkBoys.map((workBoy, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20 rounded-xl hover:from-primary-50/30 hover:to-brand-50/20 transition-all duration-300">
                  <div className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 flex items-center justify-center shadow-lg shadow-primary-500/25">
                      <span className="text-white font-bold text-sm">
                        {workBoy.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="font-bold text-gray-900">{workBoy.name}</div>
                      <div className="text-sm text-gray-500">{workBoy.tasks} tasks</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-gray-900">₹{workBoy.revenue.toLocaleString()}</div>
                    <div className="flex items-center text-warning-500">
                      <StarIcon className="h-3 w-3 mr-1" />
                      <span className="text-sm font-bold">{workBoy.rating}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </PremiumCardContent>
        </PremiumCard>
      </div>
    </div>
  )
}

export default AnalyticsPage
