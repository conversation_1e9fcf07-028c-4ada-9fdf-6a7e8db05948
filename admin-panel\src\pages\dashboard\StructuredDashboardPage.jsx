import React, { useState, useEffect } from 'react'
import { Helmet } from 'react-helmet-async'
import {
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  PlusIcon,
  SparklesIcon,
  FireIcon,
  BoltIcon,
  StarIcon,
  TrendingUpIcon,
  CalendarDaysIcon,
  MapPinIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline'

// Premium Stats Card Component
const StatsCard = ({ title, value, change, changeType, icon: Icon, color = 'primary', trend, description }) => {
  const colorClasses = {
    primary: {
      gradient: 'from-primary-500 via-primary-600 to-brand-600',
      bg: 'from-primary-50 to-brand-50/30',
      text: 'text-primary-700',
      glow: 'shadow-primary-500/25 hover:shadow-primary-500/40'
    },
    success: {
      gradient: 'from-success-500 via-success-600 to-success-700',
      bg: 'from-success-50 to-success-100/30',
      text: 'text-success-700',
      glow: 'shadow-success-500/25 hover:shadow-success-500/40'
    },
    warning: {
      gradient: 'from-warning-500 via-warning-600 to-warning-700',
      bg: 'from-warning-50 to-warning-100/30',
      text: 'text-warning-700',
      glow: 'shadow-warning-500/25 hover:shadow-warning-500/40'
    },
    accent: {
      gradient: 'from-accent-500 via-accent-600 to-accent-700',
      bg: 'from-accent-50 to-accent-100/30',
      text: 'text-accent-700',
      glow: 'shadow-accent-500/25 hover:shadow-accent-500/40'
    }
  }

  const colors = colorClasses[color] || colorClasses.primary

  return (
    <div className="group relative overflow-hidden">
      <div className={`absolute inset-0 bg-gradient-to-br ${colors.bg} rounded-2xl opacity-50 group-hover:opacity-70 transition-opacity duration-300`}></div>
      <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 p-6 hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group-hover:border-gray-300/60">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <p className="text-sm font-bold text-gray-600 uppercase tracking-wide">{title}</p>
              {trend && (
                <div className="flex items-center">
                  <TrendingUpIcon className="h-3 w-3 text-success-500" />
                </div>
              )}
            </div>
            <p className="text-3xl font-black text-gray-900 mb-3 group-hover:text-primary-700 transition-colors duration-300">
              {value}
            </p>
            {description && (
              <p className="text-xs text-gray-500 mb-2 font-medium">{description}</p>
            )}
            {change && (
              <div className="flex items-center space-x-2">
                <div className={`flex items-center px-2 py-1 rounded-full text-xs font-bold ${
                  changeType === 'positive'
                    ? 'bg-success-100 text-success-700'
                    : 'bg-danger-100 text-danger-700'
                }`}>
                  {changeType === 'positive' ? (
                    <ArrowUpIcon className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDownIcon className="h-3 w-3 mr-1" />
                  )}
                  {change}
                </div>
                <span className="text-xs text-gray-500 font-medium">vs last month</span>
              </div>
            )}
          </div>
          <div className="relative">
            <div className={`p-4 rounded-2xl bg-gradient-to-br ${colors.gradient} shadow-lg ${colors.glow} transition-all duration-300 group-hover:scale-110 group-hover:rotate-3`}>
              <Icon className="h-7 w-7 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 h-4 w-4 bg-gradient-to-br from-accent-400 to-accent-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Premium Chart Component
const PremiumChart = ({ title, data, type = 'bar', subtitle, icon: Icon }) => {
  const maxValue = Math.max(...data.map(d => d.value))

  return (
    <div className="group relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-primary-50/20 rounded-2xl opacity-50 group-hover:opacity-70 transition-opacity duration-300"></div>
      <div className="relative bg-white/90 backdrop-blur-sm rounded-2xl border border-gray-200/60 p-6 hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group-hover:border-gray-300/60">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            {Icon && (
              <div className="p-2 rounded-xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
                <Icon className="h-5 w-5 text-white" />
              </div>
            )}
            <div>
              <h3 className="text-lg font-bold text-gray-900 group-hover:text-primary-700 transition-colors duration-300">
                {title}
              </h3>
              {subtitle && (
                <p className="text-sm text-gray-500 font-medium">{subtitle}</p>
              )}
            </div>
          </div>
          <button className="px-4 py-2 text-sm text-primary-600 hover:text-primary-700 hover:bg-primary-50 rounded-xl font-bold transition-all duration-300 hover:scale-105">
            View Details
          </button>
        </div>

        {type === 'bar' && (
          <div className="space-y-5">
            {data.map((item, index) => (
              <div key={index} className="group/item">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm text-gray-700 font-bold">{item.label}</div>
                  <div className="text-sm font-black text-gray-900">₹{item.value.toLocaleString()}</div>
                </div>
                <div className="relative">
                  <div className="bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div
                      className="bg-gradient-to-r from-primary-500 via-primary-600 to-brand-600 h-3 rounded-full transition-all duration-1000 ease-out shadow-sm group-hover/item:shadow-primary-500/30"
                      style={{
                        width: `${(item.value / maxValue) * 100}%`,
                        animationDelay: `${index * 100}ms`
                      }}
                    />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-full animate-shimmer"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {type === 'list' && (
          <div className="space-y-3">
            {data.map((item, index) => (
              <div key={index} className="group/item flex items-center justify-between p-4 bg-gradient-to-r from-gray-50/50 to-primary-50/20 rounded-xl hover:from-primary-50/30 hover:to-brand-50/20 transition-all duration-300 hover:scale-[1.02]">
                <div className="flex items-center space-x-4">
                  <div className={`w-3 h-3 rounded-full shadow-sm ${
                    item.status === 'completed' ? 'bg-gradient-to-r from-success-400 to-success-500 animate-pulse' :
                    item.status === 'pending' ? 'bg-gradient-to-r from-warning-400 to-warning-500 animate-pulse' :
                    'bg-gradient-to-r from-danger-400 to-danger-500 animate-pulse'
                  }`} />
                  <div>
                    <p className="text-sm font-bold text-gray-900 group-hover/item:text-primary-700 transition-colors duration-300">
                      {item.label}
                    </p>
                    <p className="text-xs text-gray-500 font-medium">{item.time}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-black text-gray-900 group-hover/item:text-primary-700 transition-colors duration-300">
                    {item.value}
                  </span>
                  <div className="w-2 h-2 bg-primary-400 rounded-full opacity-0 group-hover/item:opacity-100 transition-opacity duration-300"></div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export const StructuredDashboardPage = () => {
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000)
    return () => clearTimeout(timer)
  }, [])

  const statsData = [
    {
      title: 'Total Users',
      value: '12,847',
      change: '+12.5%',
      changeType: 'positive',
      icon: UsersIcon,
      color: 'primary',
      description: 'Active customers & work-boys',
      trend: true
    },
    {
      title: 'Active Tasks',
      value: '1,234',
      change: '+8.2%',
      changeType: 'positive',
      icon: ClipboardDocumentListIcon,
      color: 'success',
      description: 'Currently in progress',
      trend: true
    },
    {
      title: 'Monthly Revenue',
      value: '₹91,247',
      change: '+15.3%',
      changeType: 'positive',
      icon: CurrencyDollarIcon,
      color: 'warning',
      description: 'Total earnings this month',
      trend: true
    },
    {
      title: 'Completion Rate',
      value: '94.2%',
      change: '+2.1%',
      changeType: 'positive',
      icon: ChartBarIcon,
      color: 'accent',
      description: 'Tasks completed successfully',
      trend: true
    }
  ]

  const revenueData = [
    { label: 'Jan', value: 45000 },
    { label: 'Feb', value: 52000 },
    { label: 'Mar', value: 48000 },
    { label: 'Apr', value: 61000 },
    { label: 'May', value: 55000 },
    { label: 'Jun', value: 67000 }
  ]

  const activityData = [
    { label: 'New user registered', value: 'John Doe', time: '2 min ago', status: 'completed' },
    { label: 'Task completed', value: 'WorkBoy #1234', time: '5 min ago', status: 'completed' },
    { label: 'Payment received', value: '$2,500', time: '12 min ago', status: 'completed' },
    { label: 'New task created', value: 'Delivery #5678', time: '18 min ago', status: 'pending' },
    { label: 'Issue reported', value: 'Customer #9012', time: '25 min ago', status: 'pending' }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl p-6 border border-gray-200">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8 animate-fade-in-up">
      <Helmet>
        <title>Dashboard - WorkBoy Admin</title>
      </Helmet>

      {/* Premium Header */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/10 via-brand-500/5 to-accent-600/10 rounded-3xl"></div>
        <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl border border-gray-200/60 p-8 shadow-xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="relative">
                <div className="h-16 w-16 rounded-2xl bg-gradient-to-br from-primary-600 via-primary-500 to-brand-600 flex items-center justify-center shadow-xl shadow-primary-500/30">
                  <SparklesIcon className="h-8 w-8 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 h-6 w-6 bg-gradient-to-br from-accent-400 to-accent-500 rounded-full flex items-center justify-center animate-pulse">
                  <FireIcon className="h-3 w-3 text-white" />
                </div>
              </div>
              <div>
                <h1 className="text-4xl font-black bg-gradient-to-r from-gray-900 via-primary-800 to-brand-700 bg-clip-text text-transparent">
                  Dashboard
                </h1>
                <p className="text-gray-600 mt-2 font-medium text-lg">
                  Welcome back! Here's what's happening today.
                </p>
                <div className="flex items-center mt-2 space-x-4">
                  <div className="flex items-center space-x-2">
                    <div className="h-2 w-2 bg-success-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-success-600 font-bold">System Online</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CalendarDaysIcon className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-500 font-medium">
                      {new Date().toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <button className="inline-flex items-center px-6 py-3 border border-gray-300 rounded-2xl text-sm font-bold text-gray-700 bg-white/80 hover:bg-white hover:border-primary-300 hover:text-primary-700 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <EyeIcon className="h-5 w-5 mr-2" />
                View Reports
              </button>
              <button className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-600 to-brand-600 text-white rounded-2xl text-sm font-bold hover:from-primary-700 hover:to-brand-700 transition-all duration-300 hover:scale-105 shadow-lg shadow-primary-500/30 hover:shadow-primary-500/50">
                <PlusIcon className="h-5 w-5 mr-2" />
                Add New
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Premium Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsData.map((stat, index) => (
          <div key={index} className="animate-fade-in-up" style={{ animationDelay: `${index * 100}ms` }}>
            <StatsCard
              title={stat.title}
              value={stat.value}
              change={stat.change}
              changeType={stat.changeType}
              icon={stat.icon}
              color={stat.color}
              description={stat.description}
              trend={stat.trend}
            />
          </div>
        ))}
      </div>

      {/* Premium Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="animate-fade-in-left">
          <PremiumChart
            title="Monthly Revenue"
            subtitle="Revenue trends over the last 6 months"
            data={revenueData}
            type="bar"
            icon={CurrencyDollarIcon}
          />
        </div>
        <div className="animate-fade-in-right">
          <PremiumChart
            title="Recent Activity"
            subtitle="Latest system activities and updates"
            data={activityData}
            type="list"
            icon={BoltIcon}
          />
        </div>
      </div>

      {/* Premium Quick Actions */}
      <div className="relative overflow-hidden animate-fade-in-up">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-primary-50/20 rounded-3xl opacity-50"></div>
        <div className="relative bg-white/90 backdrop-blur-sm rounded-3xl border border-gray-200/60 p-8 shadow-xl">
          <div className="flex items-center space-x-4 mb-8">
            <div className="p-3 rounded-2xl bg-gradient-to-br from-primary-500 to-brand-600 shadow-lg shadow-primary-500/25">
              <BoltIcon className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-black text-gray-900">Quick Actions</h3>
              <p className="text-gray-600 font-medium">Frequently used admin functions</p>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                title: 'Manage Users',
                icon: UsersIcon,
                color: 'primary',
                description: 'View & manage customers',
                count: '12.8k'
              },
              {
                title: 'View Tasks',
                icon: ClipboardDocumentListIcon,
                color: 'success',
                description: 'Monitor active tasks',
                count: '1.2k'
              },
              {
                title: 'Check Payments',
                icon: CurrencyDollarIcon,
                color: 'warning',
                description: 'Review transactions',
                count: '₹91k'
              },
              {
                title: 'View Analytics',
                icon: ChartBarIcon,
                color: 'accent',
                description: 'Performance insights',
                count: '94%'
              }
            ].map((action, index) => (
              <button
                key={index}
                className="group relative overflow-hidden p-6 border border-gray-200/60 rounded-2xl hover:bg-gradient-to-br hover:from-primary-50/30 hover:to-brand-50/20 transition-all duration-300 text-left hover:scale-105 hover:shadow-xl hover:border-primary-300/60"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-br ${
                    action.color === 'primary' ? 'from-primary-500 to-brand-600 shadow-primary-500/25' :
                    action.color === 'success' ? 'from-success-500 to-success-600 shadow-success-500/25' :
                    action.color === 'warning' ? 'from-warning-500 to-warning-600 shadow-warning-500/25' :
                    'from-accent-500 to-accent-600 shadow-accent-500/25'
                  } shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-black text-gray-900 group-hover:text-primary-700 transition-colors duration-300">
                      {action.count}
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-bold text-gray-900 group-hover:text-primary-700 transition-colors duration-300 mb-1">
                    {action.title}
                  </h4>
                  <p className="text-sm text-gray-500 font-medium">{action.description}</p>
                </div>
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-brand-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
