import React, { useState } from 'react'
import { Outlet } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { StructuredSidebar } from './StructuredSidebar'
import { StructuredHeader } from './StructuredHeader'

export const StructuredAdminLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      <Helmet>
        <title>Admin Dashboard - WorkBoy</title>
      </Helmet>
      
      {/* Sidebar */}
      <StructuredSidebar open={sidebarOpen} setOpen={setSidebarOpen} />
      
      {/* Main content */}
      <div className="lg:pl-64">
        {/* Header */}
        <StructuredHeader onMenuClick={() => setSidebarOpen(true)} />
        
        {/* Page content */}
        <main className="py-8">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children || <Outlet />}
          </div>
        </main>
      </div>
    </div>
  )
}
