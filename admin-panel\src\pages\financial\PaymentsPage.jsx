import React from 'react'
import { Helmet } from 'react-helmet-async'
import { CreditCardIcon } from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'

const PaymentsPage = () => {
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>Payments - WorkBoy Admin</title>
      </Helmet>
      
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <CreditCardIcon className="mr-3 h-8 w-8 text-primary-600" />
            Payments
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage payments and payment methods
          </p>
        </div>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Payment Management</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Payment data will appear here when available.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default PaymentsPage
