import React from 'react'

export const TailwindTest = () => {
  return (
    <div className="p-8 bg-white rounded-lg shadow-lg max-w-md mx-auto mt-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">Tailwind CSS Test</h2>
      <div className="space-y-4">
        <div className="p-4 bg-blue-100 text-blue-800 rounded-lg">
          <p className="font-semibold">Blue Background</p>
          <p className="text-sm">This should have a blue background</p>
        </div>
        <div className="p-4 bg-green-100 text-green-800 rounded-lg">
          <p className="font-semibold">Green Background</p>
          <p className="text-sm">This should have a green background</p>
        </div>
        <div className="p-4 bg-red-100 text-red-800 rounded-lg">
          <p className="font-semibold">Red Background</p>
          <p className="text-sm">This should have a red background</p>
        </div>
        <button className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
          Test Button
        </button>
      </div>
    </div>
  )
}

export default TailwindTest
