export const BasicTest = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '20px' }}>
        CSS Test Page
      </h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Inline Styles (Should Work)</h2>
        <div style={{ 
          backgroundColor: '#3b82f6', 
          color: 'white', 
          padding: '10px', 
          borderRadius: '8px',
          marginBottom: '10px'
        }}>
          Blue box with inline styles
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Tailwind Classes (May Not Work)</h2>
        <div className="bg-blue-500 text-white p-4 rounded-lg mb-4">
          Blue box with Tailwind classes
        </div>
        <div className="bg-green-500 text-white p-4 rounded-lg mb-4">
          Green box with Tailwind classes
        </div>
        <div className="bg-red-500 text-white p-4 rounded-lg mb-4">
          Red box with Tailwind classes
        </div>
      </div>

      <div>
        <h2 style={{ fontSize: '18px', marginBottom: '10px' }}>Mixed Styles</h2>
        <button 
          className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
          style={{ marginRight: '10px' }}
        >
          Tailwind Button
        </button>
        <button 
          style={{ 
            backgroundColor: '#059669',
            color: 'white',
            fontWeight: 'bold',
            padding: '8px 16px',
            borderRadius: '4px',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Inline Style Button
        </button>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f3f4f6', borderRadius: '8px' }}>
        <p><strong>Instructions:</strong></p>
        <ul style={{ marginLeft: '20px', marginTop: '10px' }}>
          <li>If you see colored boxes above, Tailwind CSS is working</li>
          <li>If boxes appear unstyled (no colors), there's a Tailwind configuration issue</li>
          <li>Inline styles should always work regardless of Tailwind</li>
        </ul>
      </div>
    </div>
  )
}

export default BasicTest
