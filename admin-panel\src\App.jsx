import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from '@context/AuthContext'
import { ProtectedRoute } from '@components/auth/ProtectedRoute'
import { StructuredAdminLayout } from '@components/layout/StructuredAdminLayout'

// Auth Pages
import LoginPage from '@pages/auth/LoginPage'
import ForgotPasswordPage from '@pages/auth/ForgotPasswordPage'

// Dashboard Pages
import { StructuredDashboardPage } from '@pages/dashboard/StructuredDashboardPage'

// User Management Pages
import UsersPage from '@pages/users/UsersPage'
import UserDetailPage from '@pages/users/UserDetailPage'
import WorkBoysPage from '@pages/users/WorkBoysPage'
import WorkBoyDetailPage from '@pages/users/WorkBoyDetailPage'
import KYCVerificationPage from '@pages/users/KYCVerificationPage'

// Task Management Pages
import TasksPage from '@pages/tasks/TasksPage'
import TaskDetailPage from '@pages/tasks/TaskDetailPage'
import CategoriesPage from '@pages/tasks/CategoriesPage'

// Financial Pages
import PaymentsPage from '@pages/financial/PaymentsPage'
import TransactionsPage from '@pages/financial/TransactionsPage'
import ReportsPage from '@pages/financial/ReportsPage'

// Settings Pages
import SettingsPage from '@pages/settings/SettingsPage'
import AdminUsersPage from '@pages/settings/AdminUsersPage'

// Error Pages
import NotFoundPage from '@pages/error/NotFoundPage'

function App() {
  return (
    <AuthProvider>
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        
        {/* Protected Admin Routes */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <StructuredAdminLayout>
                <Routes>
                  {/* Dashboard */}
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<StructuredDashboardPage />} />
                  
                  {/* User Management */}
                  <Route path="/users" element={<UsersPage />} />
                  <Route path="/users/:id" element={<UserDetailPage />} />
                  <Route path="/workboys" element={<WorkBoysPage />} />
                  <Route path="/workboys/:id" element={<WorkBoyDetailPage />} />
                  <Route path="/kyc-verification" element={<KYCVerificationPage />} />
                  
                  {/* Task Management */}
                  <Route path="/tasks" element={<TasksPage />} />
                  <Route path="/tasks/:id" element={<TaskDetailPage />} />
                  <Route path="/categories" element={<CategoriesPage />} />
                  
                  {/* Financial Management */}
                  <Route path="/payments" element={<PaymentsPage />} />
                  <Route path="/transactions" element={<TransactionsPage />} />
                  <Route path="/reports" element={<ReportsPage />} />
                  
                  {/* Settings */}
                  <Route path="/settings" element={<SettingsPage />} />
                  <Route path="/admin-users" element={<AdminUsersPage />} />
                  
                  {/* 404 */}
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </StructuredAdminLayout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </AuthProvider>
  )
}

export default App
