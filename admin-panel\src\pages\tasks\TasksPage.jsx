import React from 'react'
import { Helmet } from 'react-helmet-async'
import { ClipboardDocumentListIcon, PlusIcon } from '@heroicons/react/24/outline'
import { Card, CardHeader, CardTitle, CardContent } from '@components/ui/Card'
import { Button } from '@components/ui/Button'

const TasksPage = () => {
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <Helmet>
        <title>Tasks - WorkBoy Admin</title>
      </Helmet>
      
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 flex items-center">
            <ClipboardDocumentListIcon className="mr-3 h-8 w-8 text-primary-600" />
            Tasks
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage all tasks and service requests
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Button className="inline-flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Create Task
          </Button>
        </div>
      </div>

      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Task Management</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <ClipboardDocumentListIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No tasks found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating a new task.
              </p>
              <div className="mt-6">
                <Button>
                  <PlusIcon className="mr-2 h-4 w-4" />
                  Create Task
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default TasksPage
